import { NextResponse } from "next/server"
import prisma from "@/lib/db"
import { syncNewProductToInventory } from "@/lib/services/product-inventory-sync"
import {
  parsePaginationFromSearchParams,
  buildPaginatedResponse,
  getPrismaQueryOptions,
  validatePaginationParams,
  getPaginationLogInfo
} from "@/lib/pagination-utils"
import { ErrorUtils } from "@/lib/error-utils"

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    
    // 解析分页参数
    const paginationParams = parsePaginationFromSearchParams(searchParams)
    
    // 验证分页参数
    const validation = validatePaginationParams({
      page: paginationParams.page,
      pageSize: paginationParams.pageSize,
      offset: paginationParams.offset,
      limit: paginationParams.limit
    })
    
    if (!validation.isValid) {
      return NextResponse.json({ 
        error: "Invalid pagination parameters", 
        details: validation.errors 
      }, { status: 400 })
    }

    console.log(`[GET /api/products] 查询产品列表 - ${getPaginationLogInfo(paginationParams)}`)

    // 构建查询条件
    const whereClause = {
      type: {
        notIn: ["category_placeholder", "unit_placeholder", "material_placeholder"]
      }
    }

    // 获取总数
    const total = await prisma.product.count({
      where: whereClause
    })

    // 获取分页查询选项
    const queryOptions = getPrismaQueryOptions(paginationParams)

    // 获取分页数据
    const products = await prisma.product.findMany({
      where: whereClause,
      include: {
        productCategory: true,
        productTags: {
          include: {
            tag: true,
          },
        },
      },
      orderBy: {
        id: "asc",
      },
      ...queryOptions
    })

    // 转换为前端期望的格式
    const formattedProducts = products.map(product => ({
      ...product,
      categoryName: product.productCategory?.name || null,
      tags: product.productTags?.map(pt => pt.tag) || [],
      tagIds: product.productTags?.map(pt => pt.tagId) || [],
    }))

    // 获取材质数据（用于过滤器）
    const materialsData = await prisma.product.findMany({
      where: {
        material: { not: null },
        type: { notIn: ["category_placeholder", "unit_placeholder", "material_placeholder"] }
      },
      select: { material: true },
      distinct: ['material']
    })

    const materials = materialsData
      .map(item => item.material)
      .filter(material => material && material.trim() !== '')
      .sort()
      .map(material => ({
        name: material,
        id: material
      }))

    // 构建分页响应，添加材质数据
    const response = {
      ...buildPaginatedResponse(formattedProducts, total, paginationParams),
      materials: materials
    }

    console.log(`[GET /api/products] 返回 ${formattedProducts.length}/${total} 个产品`)

    return NextResponse.json(response)
  } catch (error) {
    const appError = await ErrorUtils.handleError(error, 'products-api');
    const errorResponse = ErrorUtils.formatErrorResponse(appError);

    return NextResponse.json(errorResponse, {
      status: appError.type === ErrorUtils.ErrorType.VALIDATION ? 400 : 500
    });
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json()
    console.log("🔄 [POST /api/products] 接收到的产品数据:", data)

    // 验证必填字段 - 只验证产品名称
    if (!data.name || typeof data.name !== 'string' || data.name.trim() === '') {
      const validationError = new ErrorUtils.ValidationError("产品名称为必填项", { field: 'name' }, 'products-api');
      const errorResponse = ErrorUtils.formatErrorResponse(validationError);
      return NextResponse.json(errorResponse, { status: 400 });
    }

    // 验证价格（如果提供）- 价格可以为空，但不能为负数
    let validatedPrice = 0; // 默认价格为0
    if (data.price !== null && data.price !== undefined) {
      const priceValue = Number(data.price);
      if (isNaN(priceValue) || priceValue < 0) {
        const validationError = new ErrorUtils.ValidationError("产品价格不能为负数", { field: 'price', value: data.price }, 'products-api');
        const errorResponse = ErrorUtils.formatErrorResponse(validationError);
        return NextResponse.json(errorResponse, { status: 400 });
      }
      validatedPrice = priceValue;
    }

    // 验证佣金率（如果提供）- 佣金率可以为空，但不能为负数
    let validatedCommissionRate = 0; // 默认佣金率为0
    if (data.commissionRate !== null && data.commissionRate !== undefined) {
      const commissionValue = Number(data.commissionRate);
      if (isNaN(commissionValue) || commissionValue < 0) {
        const validationError = new ErrorUtils.ValidationError("佣金率不能为负数", { field: 'commissionRate', value: data.commissionRate }, 'products-api');
        const errorResponse = ErrorUtils.formatErrorResponse(validationError);
        return NextResponse.json(errorResponse, { status: 400 });
      }
      validatedCommissionRate = commissionValue;
    }

    // 准备产品数据 - 排除指定字段
    const productData = {
      name: data.name.trim(),
      price: validatedPrice, // 使用验证后的价格
      commissionRate: validatedCommissionRate, // 添加必需的佣金率字段
      type: data.type || "product",
      imageUrl: data.imageUrl || null,
      imageUrls: data.imageUrls || [],
      description: data.description || null,
      categoryId: data.categoryId === "uncategorized" ? null : data.categoryId ? parseInt(data.categoryId) : null,
      barcode: data.barcode || null,
      dimensions: data.dimensions || null,
      material: data.material || "珐琅", // 保留用于兼容性
      unit: data.unit || "套", // 保留用于兼容性
      inventory: data.inventory ? Number.parseInt(data.inventory) : null,
    }

    console.log("🔄 [POST /api/products] 处理后的产品数据:", productData)

    // 提取标签ID
    const tagIds = data.tagIds || []

    try {
      // 使用事务创建产品和标签关联
      const product = await prisma.$transaction(async (tx) => {
        // 创建产品
        const newProduct = await tx.product.create({
          data: productData,
        })

        // 创建标签关联
        if (tagIds.length > 0) {
          await tx.productTagsOnProducts.createMany({
            data: tagIds.map((tagId: number) => ({
              productId: newProduct.id,
              tagId: tagId,
            })),
          })
        }

        // 返回包含关联数据的产品
        return await tx.product.findUnique({
          where: { id: newProduct.id },
          include: {
            productCategory: true,
            productTags: {
              include: {
                tag: true,
              },
            },
          },
        })
      })

      // 转换为前端期望的格式
      const formattedProduct = {
        ...product,
        categoryName: product?.productCategory?.name || null,
        tags: product?.productTags?.map(pt => pt.tag) || [],
        tagIds: product?.productTags?.map(pt => pt.tagId) || [],
      }

      console.log("✅ [POST /api/products] 产品创建成功:", formattedProduct.id)

      // 异步同步到库存模块
      syncNewProductToInventory(product.id).then(syncResult => {
        if (syncResult.success) {
          console.log(`✅ [ProductSync] 产品 ${product.id} 同步到库存模块成功`)
        } else {
          console.error(`❌ [ProductSync] 产品 ${product.id} 同步到库存模块失败:`, syncResult.message)
        }
      }).catch(error => {
        console.error(`❌ [ProductSync] 产品 ${product.id} 同步异常:`, error)
      })

      return NextResponse.json({
        success: true,
        product: formattedProduct,
        syncStatus: "pending" // 表示同步正在进行中
      })
    } catch (dbError) {
      const appError = await ErrorUtils.handleError(dbError, 'products-api-database');
      const errorResponse = ErrorUtils.formatErrorResponse(appError);
      return NextResponse.json(errorResponse, { status: 500 });
    }
  } catch (error) {
    const appError = await ErrorUtils.handleError(error, 'products-api');
    const errorResponse = ErrorUtils.formatErrorResponse(appError);
    return NextResponse.json(errorResponse, { status: 500 });
  }
}
