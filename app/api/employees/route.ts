import { NextResponse } from "next/server"
import prisma from "@/lib/db"
import { ErrorUtils } from "@/lib/error-utils"

export async function GET() {
  try {
    const employees = await prisma.employee.findMany({
      orderBy: {
        id: "asc",
      },
    })

    return NextResponse.json(employees)
  } catch (error) {
    const appError = await ErrorUtils.handleError(error, 'employees-api');
    const errorResponse = ErrorUtils.formatErrorResponse(appError);

    return NextResponse.json(errorResponse, {
      status: appError.type === ErrorUtils.ErrorType.VALIDATION ? 400 : 500
    });
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json()

    const employee = await prisma.employee.create({
      data: {
        name: data.name,
        position: data.position,
        phone: data.phone,
        email: data.email,
        dailySalary: Number.parseFloat(data.dailySalary),
        status: data.status || "active",
      },
    })

    return NextResponse.json(employee)
  } catch (error) {
    const appError = await ErrorUtils.handleError(error, 'employees-api-create');
    const errorResponse = ErrorUtils.formatErrorResponse(appError);

    return NextResponse.json(errorResponse, { status: 500 });
  }
}
